<template>
  <div class="main-layout">
    <!-- 顶部导航栏 -->
    <header class="top-nav">
      <div class="system-info">
        <h1>项目管理系统</h1>
      </div>

      <!-- 登录/退出按钮 -->
      <div class="auth-container">
        <span v-if="userInfo" class="username">
          {{ userInfo.username }} ({{ store.getters.userRole }})
          <small style="color: #999; margin-left: 10px;">
            权限: {{ store.getters.permissions.join(', ') }}
          </small>
        </span>
        <button v-if="!userInfo" @click="showLoginModal = true" class="auth-btn">登录</button>
        <button v-else @click="handleLogout" class="auth-btn logout-btn">退出登录</button>
      </div>
    </header>

    <!-- 登录模态框 -->
    <LoginModal
      v-if="showLoginModal"
      @close="showLoginModal = false"
      @login-success="handleLoginSuccess"
    />

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧菜单 -->
      <aside class="sidebar">
        <nav class="sidebar-nav">
          <ul class="menu-list">
            <li v-for="item in menuItems" :key="item.path" class="menu-item">
              <a
                href="javascript:void(0)"
                :class="{
                  'menu-link': true,
                  'router-link-exact-active': $route.path === item.path,
                }"
                @click="handleMenuClick(item.path)"
              >
                {{ item.name }}
              </a>
            </li>
          </ul>
        </nav>

        <!-- 时间显示在菜单底部 -->
        <div class="sidebar-footer">
          <div class="current-time">
            <span class="time-label">当前时间</span>
            <span class="time-value">{{ currentTime }}</span>
          </div>
        </div>
      </aside>

      <!-- 右侧内容 -->
      <main class="content">
        <router-view></router-view>
      </main>
    </div>

    <!-- 底部固定信息 -->
    <footer class="footer">
      <p>© 2025 jooviyo 版权所有</p>
    </footer>
  </div>
</template>
<script>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { logout } from '@/utils/api';
import { showLogoutSuccess, showWarning, showErrorToast } from '@/utils/toast';
import LoginModal from '@/views/LoginModal.vue';

export default {
  name: 'MainLayout',
  components: { LoginModal },
  setup() {
    const router = useRouter();
    const store = useStore();

    const currentTime = ref('');
    const timer = ref(null);
    const showLoginModal = ref(false);

    // 使用computed来响应式地获取用户信息
    const userInfo = computed(() => store.getters.currentUser);

    const allMenuItems = [
      {
        path: '/dashboard',
        name: '仪表盘',
        requiresAuth: false, // 所有用户都可以访问
        permission: 'dashboard'
      },
      {
        path: '/projects',
        name: '项目管理',
        requiresAuth: true, // 需要登录
        permission: 'projects'
      },
      {
        path: '/tasks',
        name: '任务管理',
        requiresAuth: true, // 需要登录
        permission: 'tasks'
      },
      {
        path: '/UserDashboard',
        name: '个人看板',
        requiresAuth: true, // 需要登录
        permission: 'tasks'
      },
      {
        path: '/organization',
        name: '组织管理',
        requiresAuth: true, // 需要登录
        permission: 'organization',
        adminOnly: true // 只有管理员和经理可以访问
      },
      {
        path: '/users',
        name: '人员管理',
        requiresAuth: true, // 需要登录
        permission: 'personnel',
        adminOnly: true // 只有管理员可以访问
      },
    ];

    const menuItems = computed(() => {
      console.log('=== 菜单计算 ===');
      console.log('当前用户信息:', userInfo.value);
      console.log('是否已登录:', store.getters.isAuthenticated);
      console.log('用户权限:', store.getters.permissions);

      // 未登录用户只显示仪表盘
      if (!store.getters.isAuthenticated) {
        console.log('未登录，只显示仪表盘');
        return allMenuItems.filter(item => !item.requiresAuth);
      }

      // 已登录用户根据权限显示菜单
      const filteredItems = allMenuItems.filter(item => {
        // 仪表盘所有人都可以访问
        if (!item.requiresAuth) {
          return true;
        }

        // 管理功能：根据具体权限检查
        if (item.adminOnly) {
          let canAccess = false;
          if (item.permission === 'personnel') {
            canAccess = store.getters.canAccessPersonnel;
            console.log(`人员管理权限检查: ${canAccess}`);
          } else if (item.permission === 'organization') {
            canAccess = store.getters.canAccessOrganization;
            console.log(`组织管理权限检查: ${canAccess}`);
          }
          return canAccess;
        }

        // 其他功能：检查具体权限
        if (item.permission) {
          const hasPermission = store.getters.hasPermission(item.permission);
          console.log(`${item.name} (${item.permission}) 权限检查: ${hasPermission}`);
          return hasPermission;
        }

        // 默认需要登录
        return true;
      });

      console.log('过滤后的菜单项:', filteredItems.map(item => item.name));
      return filteredItems;
    });

    const updateTime = () => {
      const now = new Date();
      currentTime.value = now.toLocaleString('zh-CN');
    };

    const handleLogout = async () => {
      try {
        await logout();
        // 清除 Vuex 状态
        store.commit('clearUser');
        // userInfo现在是computed，会自动更新
        showLogoutSuccess();
        // 跳转到仪表盘（未登录状态）
        router.push('/dashboard');
      } catch (error) {
        console.error('退出登录失败:', error);
        // 即使API调用失败，也要清除本地状态
        store.commit('clearUser');
        // userInfo现在是computed，会自动更新
        showLogoutSuccess();
        router.push('/dashboard');
      }
    };

    const handleMenuClick = path => {
      console.log('=== 菜单点击 ===');
      console.log('点击路径:', path);

      const menuItem = allMenuItems.find(item => item.path === path);
      console.log('找到的菜单项:', menuItem);
      console.log('当前用户信息:', userInfo.value);
      console.log('是否已登录:', store.getters.isAuthenticated);

      // 仪表盘所有人都可以访问
      if (path === '/dashboard') {
        console.log('跳转到仪表盘');
        router.push(path);
        return;
      }

      // 其他功能需要登录
      if (menuItem && menuItem.requiresAuth && !store.getters.isAuthenticated) {
        console.log('用户未登录，显示登录框');
        showWarning('请先登录', '请先登录后再访问此功能');
        showLoginModal.value = true;
        return;
      }

      // 已登录用户的权限检查
      if (menuItem && store.getters.isAuthenticated) {
        console.log('用户已登录，检查权限');

        // 管理功能权限检查
        if (menuItem.adminOnly) {
          console.log('检查管理功能权限，权限类型:', menuItem.permission);
          let hasAccess = false;
          let errorMessage = '';

          if (menuItem.permission === 'personnel') {
            hasAccess = store.getters.canAccessPersonnel;
            errorMessage = '只有管理员和项目经理可以访问人员管理功能';
            console.log('人员管理权限检查结果:', hasAccess);
          } else if (menuItem.permission === 'organization') {
            hasAccess = store.getters.canAccessOrganization;
            errorMessage = '只有管理员和经理可以访问组织管理功能';
            console.log('组织管理权限检查结果:', hasAccess);
            console.log('用户角色:', store.getters.userRole);
            console.log('canAccessOrganization:', store.getters.canAccessOrganization);
          }

          if (!hasAccess) {
            console.log('权限不足:', errorMessage);
            showErrorToast('权限不足', errorMessage);
            return;
          }
        }

        // 项目管理和任务管理：登录用户都可以访问
        else if (menuItem.permission && menuItem.permission !== 'dashboard') {
          console.log('检查普通权限:', menuItem.permission);
          const hasPermission = store.getters.hasPermission(menuItem.permission);
          console.log('权限检查结果:', hasPermission);

          if (!hasPermission) {
            console.log('普通权限不足');
            showErrorToast(
              '权限不足',
              '您没有权限访问此功能，请联系管理员'
            );
            return;
          }
        }
      }

      console.log('权限检查通过，跳转到:', path);
      router.push(path);
    };

    onMounted(() => {
      updateTime();
      timer.value = setInterval(updateTime, 1000);

      // 初始化时检查用户状态和权限
      console.log('=== MainLayout 初始化 ===');
      console.log('当前用户信息:', userInfo.value);
      console.log('是否已登录:', store.getters.isAuthenticated);
      console.log('用户角色:', store.getters.userRole);
      console.log('用户权限:', store.getters.permissions);
      console.log('可访问人员管理:', store.getters.canAccessPersonnel);
      console.log('菜单项数量:', menuItems.value.length);
      console.log('菜单项:', menuItems.value.map(item => item.name));
    });

    onUnmounted(() => {
      if (timer.value) {
        clearInterval(timer.value);
      }
    });

    // 监听用户信息变化，用于调试
    watch(userInfo, (newUser, oldUser) => {
      console.log('=== 用户信息变化 ===');
      console.log('旧用户:', oldUser);
      console.log('新用户:', newUser);
      console.log('菜单更新后:', menuItems.value.map(item => item.name));
    }, { immediate: false });

    // 监听登录状态变化
    watch(() => store.getters.isAuthenticated, (newAuth, oldAuth) => {
      console.log('=== 登录状态变化 ===');
      console.log('旧状态:', oldAuth);
      console.log('新状态:', newAuth);
      console.log('菜单更新后:', menuItems.value.map(item => item.name));
    }, { immediate: false });

    // 监听权限变化
    watch(() => store.getters.permissions, (newPerms, oldPerms) => {
      console.log('=== 权限变化 ===');
      console.log('旧权限:', oldPerms);
      console.log('新权限:', newPerms);
      console.log('菜单更新后:', menuItems.value.map(item => item.name));
    }, { immediate: false });

    const handleLoginSuccess = () => {
      showLoginModal.value = false;

      // 等待一下让store状态更新
      setTimeout(() => {
        console.log('=== 登录成功后状态检查 ===');
        console.log('当前用户信息:', userInfo.value);
        console.log('是否已登录:', store.getters.isAuthenticated);
        console.log('用户角色:', store.getters.userRole);
        console.log('用户权限:', store.getters.permissions);
        console.log('可访问人员管理:', store.getters.canAccessPersonnel);
        console.log('可访问项目管理:', store.getters.canAccessProjects);
        console.log('可访问任务管理:', store.getters.canAccessTasks);
        console.log('菜单项数量:', menuItems.value.length);
        console.log('菜单项:', menuItems.value.map(item => item.name));
      }, 100);

      // 登录成功直接跳转到仪表盘，不显示提示
      router.push('/dashboard');
    };

    return {
      currentTime,
      userInfo,
      menuItems,
      showLoginModal,
      updateTime,
      handleLogout,
      handleLoginSuccess,
      handleMenuClick,
    };
  },
};
</script>

<style scoped>
.main-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(to bottom, #0f0f23, #05051a);
  color: #00ffff;
}

/* 固定顶部导航 */
.top-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 2rem;
  height: 60px;
  background: linear-gradient(135deg, #46597e 0%, #3a4a6b 100%);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  z-index: 100;
  backdrop-filter: blur(10px);
}

.system-info h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  text-shadow: 0 0 8px rgba(0, 255, 255, 0.5);
  letter-spacing: 0.5px;
}

.auth-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.username {
  color: #00ffff;
  font-weight: 500;
  font-size: 0.95rem;
}

.auth-btn {
  padding: 0.5rem 1.25rem;
  border: none;
  border-radius: 20px;
  background: linear-gradient(135deg, #00ffff 0%, #00bfff 100%);
  color: #0f0f23;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 255, 255, 0.3);
}

.auth-btn:hover {
  background: linear-gradient(135deg, #00dddd 0%, #00aaff 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 255, 255, 0.4);
}

.logout-btn {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  color: white;
  box-shadow: 0 2px 6px rgba(255, 107, 107, 0.3);
}

.logout-btn:hover {
  background: linear-gradient(135deg, #ff5252 0%, #e53935 100%);
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
}

/* 主要内容区域 - 为固定导航留出空间 */
.main-content {
  flex: 1;
  display: flex;
  padding: 2rem;
  gap: 2rem;
  margin-top: 60px; /* 为固定导航留出空间 */
}

/* 侧边栏 */
.sidebar {
  width: 200px;
  background: linear-gradient(135deg, #1a1a40 0%, #151530 100%);
  border-radius: 12px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidebar-nav {
  flex: 1;
  padding: 1.5rem 0;
}

.menu-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.menu-item {
  margin: 0.25rem 0;
}

.menu-link {
  display: block;
  padding: 0.875rem 1.5rem;
  color: #00ffff;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
  position: relative;
}

.menu-link:hover {
  background: rgba(0, 255, 255, 0.1);
  border-left-color: #00ffff;
  transform: translateX(4px);
}

.menu-link.router-link-exact-active {
  background: linear-gradient(90deg, rgba(0, 255, 255, 0.2) 0%, rgba(0, 255, 255, 0.05) 100%);
  border-left-color: #00ffff;
  color: #ffffff;
  box-shadow: inset 0 0 10px rgba(0, 255, 255, 0.1);
}

/* 侧边栏底部时间显示 */
.sidebar-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid rgba(0, 255, 255, 0.2);
  background: rgba(0, 0, 0, 0.2);
}

.current-time {
  text-align: center;
}

.time-label {
  display: block;
  font-size: 0.75rem;
  color: #7ee7ff;
  margin-bottom: 0.25rem;
  opacity: 0.8;
}

.time-value {
  display: block;
  font-size: 0.85rem;
  color: #00ffff;
  font-weight: 500;
  font-family: 'Courier New', monospace;
}

/* 主内容区域 */
.content {
  flex: 1;
  background: linear-gradient(135deg, #1a1a40 0%, #151530 100%);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
  overflow-y: auto;
}

/* 底部信息 */
.footer {
  text-align: center;
  padding: 1rem;
  background: linear-gradient(135deg, #1a1a40 0%, #151530 100%);
  color: #7ee7ff;
  font-size: 0.85rem;
  opacity: 0.8;
  border-top: 1px solid rgba(0, 255, 255, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .top-nav {
    padding: 0.5rem 1rem;
    height: 50px;
  }

  .system-info h1 {
    font-size: 1.25rem;
  }

  .main-content {
    margin-top: 50px;
    padding: 1rem;
    gap: 1rem;
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    order: 2;
  }

  .content {
    order: 1;
  }

  .auth-btn {
    padding: 0.4rem 1rem;
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .top-nav {
    padding: 0.5rem;
  }

  .system-info h1 {
    font-size: 1.1rem;
  }

  .main-content {
    padding: 0.5rem;
  }

  .sidebar {
    border-radius: 8px;
  }

  .content {
    border-radius: 8px;
    padding: 1rem;
  }
}


</style>
