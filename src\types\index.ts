// 用户相关类型
export interface User {
  id: number;
  username: string;
  email: string;
  name: string;
  avatar?: string;
  role: 'admin' | 'manager' | 'member';
  department?: string;
  phone?: string;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

// 项目相关类型
export interface Project {
  id: number;
  name: string;
  description: string;
  status: 'planning' | 'active' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  startDate: string;
  endDate: string;
  progress: number;
  managerId: number;
  manager: User;
  members: User[];
  createdAt: string;
  updatedAt: string;
}

// 任务相关类型
export interface Task {
  id: number;
  title: string;
  description: string;
  status: 'todo' | 'in_progress' | 'review' | 'done';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  projectId: number;
  project: Project;
  assigneeId?: number;
  assignee?: User;
  creatorId: number;
  creator: User;
  startDate?: string;
  dueDate: string;
  estimatedHours?: number;
  actualHours?: number;
  tags: string[];
  attachments: string[];
  createdAt: string;
  updatedAt: string;
}

// 仪表盘统计数据
export interface DashboardStats {
  totalProjects: number;
  activeProjects: number;
  completedProjects: number;
  totalTasks: number;
  completedTasks: number;
  overdueTasks: number;
  myTasks: number;
  teamMembers: number;
}

// API 响应类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 分页数据类型
export interface PaginatedData<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 表单数据类型
export interface LoginForm {
  username: string;
  password: string;
}

export interface ProjectForm {
  name: string;
  description: string;
  status: Project['status'];
  priority: Project['priority'];
  startDate: string;
  endDate: string;
  managerId: number;
  memberIds: number[];
}

export interface TaskForm {
  title: string;
  description: string;
  status: Task['status'];
  priority: Task['priority'];
  projectId: number;
  assigneeId?: number;
  startDate?: string;
  dueDate?: string;
  estimatedHours?: number;
  tags: string[];
}

export interface UserForm {
  username: string;
  email: string;
  name: string;
  role: User['role'];
  department?: string;
  phone?: string;
  password?: string;
}

// 组织架构相关类型
export interface Organization {
  id: number;
  name: string;
  code: string; // 组织编码
  type: 'company' | 'department' | 'team' | 'group'; // 组织类型
  parentId?: number; // 父级组织ID
  parent?: Organization; // 父级组织
  children?: Organization[]; // 子级组织
  managerId?: number; // 负责人ID
  manager?: User; // 负责人
  description?: string; // 描述
  level: number; // 层级，从1开始
  sort: number; // 排序
  status: 'active' | 'inactive'; // 状态
  memberCount: number; // 成员数量
  createdAt: string;
  updatedAt: string;
}

export interface OrganizationForm {
  name: string;
  code: string;
  type: Organization['type'];
  parentId?: number;
  managerId?: number;
  description?: string;
  sort: number;
  status: Organization['status'];
}
