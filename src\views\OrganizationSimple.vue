<template>
  <div class="organization-simple">
    <h1>组织管理页面</h1>
    <p>这是一个简化的组织管理页面，用于测试路由是否正常工作。</p>
    
    <div class="test-info">
      <h3>测试信息：</h3>
      <p>当前路由：{{ $route.path }}</p>
      <p>页面加载时间：{{ loadTime }}</p>
      <p>用户信息：{{ userInfo }}</p>
      <p>用户权限：{{ permissions }}</p>
    </div>

    <div class="actions">
      <button @click="goBack" class="btn">返回</button>
      <button @click="goHome" class="btn">回到首页</button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';

const router = useRouter();
const store = useStore();
const loadTime = ref('');

const userInfo = computed(() => store.getters.currentUser);
const permissions = computed(() => store.getters.permissions);

const goBack = () => {
  router.go(-1);
};

const goHome = () => {
  router.push('/dashboard');
};

onMounted(() => {
  loadTime.value = new Date().toLocaleString();
  console.log('组织管理页面已加载');
});
</script>

<style scoped>
.organization-simple {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.organization-simple h1 {
  color: #333;
  margin-bottom: 20px;
}

.test-info {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 8px;
  margin: 20px 0;
}

.test-info h3 {
  margin-top: 0;
  color: #666;
}

.test-info p {
  margin: 8px 0;
  font-family: monospace;
  background: white;
  padding: 5px;
  border-radius: 4px;
}

.actions {
  margin-top: 20px;
}

.btn {
  background: #409eff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 10px;
}

.btn:hover {
  background: #66b1ff;
}
</style>
