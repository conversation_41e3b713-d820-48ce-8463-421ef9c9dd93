<template>
  <PermissionGuard
    permission="organization"
    :role="['admin', 'manager']"
    message="只有管理员和经理可以访问组织管理功能"
  >
    <div class="organization">
      <div class="page-header">
        <h2>组织管理</h2>
        <div class="header-actions">
          <el-button type="primary" @click="showCreateDialog = true">
            <el-icon><Plus /></el-icon>
            新增组织
          </el-button>
          <el-button @click="refreshOrganizations">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>

      <!-- 搜索和筛选 -->
      <div class="search-section">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-input
              v-model="searchQuery"
              placeholder="搜索组织名称或编码"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select v-model="typeFilter" placeholder="组织类型" clearable @change="handleSearch">
              <el-option label="公司" value="company" />
              <el-option label="部门" value="department" />
              <el-option label="团队" value="team" />
              <el-option label="小组" value="group" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="statusFilter" placeholder="状态" clearable @change="handleSearch">
              <el-option label="启用" value="active" />
              <el-option label="禁用" value="inactive" />
            </el-select>
          </el-col>
        </el-row>
      </div>

      <!-- 组织架构树 -->
      <el-card class="organization-tree-card">
        <template #header>
          <span>组织架构</span>
        </template>
        <el-tree
          ref="organizationTree"
          :data="organizationTreeData"
          :props="treeProps"
          node-key="id"
          :expand-on-click-node="false"
          :default-expand-all="false"
          :default-expanded-keys="expandedKeys"
          class="organization-tree"
        >
          <template #default="{ node, data }">
            <div class="tree-node">
              <div class="node-content">
                <el-icon class="node-icon">
                  <OfficeBuilding v-if="data.type === 'company'" />
                  <Folder v-else-if="data.type === 'department'" />
                  <User v-else-if="data.type === 'team'" />
                  <UserFilled v-else />
                </el-icon>
                <span class="node-name">{{ data.name }}</span>
                <el-tag :type="getTypeTagType(data.type)" size="small" class="node-type">
                  {{ getTypeText(data.type) }}
                </el-tag>
                <el-tag :type="getStatusTagType(data.status)" size="small" class="node-status">
                  {{ getStatusText(data.status) }}
                </el-tag>
                <span class="node-member-count">({{ data.memberCount }}人)</span>
              </div>
              <div class="node-actions">
                <el-button type="primary" size="small" @click="handleEdit(data)">
                  编辑
                </el-button>
                <el-button type="success" size="small" @click="handleAddChild(data)">
                  添加下级
                </el-button>
                <el-button 
                  type="danger" 
                  size="small" 
                  @click="handleDelete(data)"
                  :disabled="data.children && data.children.length > 0"
                >
                  删除
                </el-button>
              </div>
            </div>
          </template>
        </el-tree>
      </el-card>

      <!-- 新增/编辑组织对话框 -->
      <el-dialog
        v-model="showCreateDialog"
        :title="editingOrganization ? '编辑组织' : '新增组织'"
        width="600px"
        @close="resetForm"
      >
        <el-form
          ref="organizationFormRef"
          :model="organizationForm"
          :rules="formRules"
          label-width="100px"
        >
          <el-form-item label="组织名称" prop="name">
            <el-input v-model="organizationForm.name" placeholder="请输入组织名称" />
          </el-form-item>
          <el-form-item label="组织编码" prop="code">
            <el-input v-model="organizationForm.code" placeholder="请输入组织编码" />
          </el-form-item>
          <el-form-item label="组织类型" prop="type">
            <el-select v-model="organizationForm.type" placeholder="请选择组织类型">
              <el-option label="公司" value="company" />
              <el-option label="部门" value="department" />
              <el-option label="团队" value="team" />
              <el-option label="小组" value="group" />
            </el-select>
          </el-form-item>
          <el-form-item label="上级组织" prop="parentId">
            <el-tree-select
              v-model="organizationForm.parentId"
              :data="parentSelectData"
              :props="{ label: 'name', value: 'id' }"
              placeholder="请选择上级组织"
              clearable
              check-strictly
            />
          </el-form-item>
          <el-form-item label="负责人" prop="managerId">
            <el-select v-model="organizationForm.managerId" placeholder="请选择负责人" clearable filterable>
              <el-option
                v-for="user in users"
                :key="user.id"
                :label="user.name"
                :value="user.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="描述" prop="description">
            <el-input
              v-model="organizationForm.description"
              type="textarea"
              :rows="3"
              placeholder="请输入组织描述"
            />
          </el-form-item>
          <el-form-item label="排序" prop="sort">
            <el-input-number v-model="organizationForm.sort" :min="0" :max="999" />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="organizationForm.status">
              <el-radio value="active">启用</el-radio>
              <el-radio value="inactive">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            {{ editingOrganization ? '更新' : '创建' }}
          </el-button>
        </template>
      </el-dialog>
    </div>
  </PermissionGuard>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import PermissionGuard from '@/components/PermissionGuard.vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Plus,
  Refresh,
  Search,
  OfficeBuilding,
  Folder,
  User,
  UserFilled,
} from '@element-plus/icons-vue';
import type { Organization, OrganizationForm, User as UserType } from '@/types';

// 响应式数据
const showCreateDialog = ref(false);
const editingOrganization = ref<Organization | null>(null);
const submitting = ref(false);
const searchQuery = ref('');
const typeFilter = ref('');
const statusFilter = ref('');
const expandedKeys = ref<number[]>([]);

// 表单引用
const organizationFormRef = ref();

// 组织数据
const organizations = ref<Organization[]>([]);
const users = ref<UserType[]>([]);

// 表单数据
const organizationForm = reactive<OrganizationForm>({
  name: '',
  code: '',
  type: 'department',
  parentId: undefined,
  managerId: undefined,
  description: '',
  sort: 0,
  status: 'active',
});

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入组织名称', trigger: 'blur' },
    { min: 2, max: 50, message: '组织名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入组织编码', trigger: 'blur' },
    { min: 2, max: 20, message: '组织编码长度在 2 到 20 个字符', trigger: 'blur' },
    { pattern: /^[A-Za-z0-9_-]+$/, message: '组织编码只能包含字母、数字、下划线和横线', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择组织类型', trigger: 'change' }
  ],
  sort: [
    { required: true, message: '请输入排序值', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
};

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'name'
};

// 计算属性
const organizationTreeData = computed(() => {
  return buildTree(filteredOrganizations.value);
});

const filteredOrganizations = computed(() => {
  let filtered = organizations.value;

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(org =>
      org.name.toLowerCase().includes(query) ||
      org.code.toLowerCase().includes(query)
    );
  }

  if (typeFilter.value) {
    filtered = filtered.filter(org => org.type === typeFilter.value);
  }

  if (statusFilter.value) {
    filtered = filtered.filter(org => org.status === statusFilter.value);
  }

  return filtered;
});

const parentSelectData = computed(() => {
  // 编辑时排除自己和子级组织
  const excludeIds = editingOrganization.value
    ? [editingOrganization.value.id, ...getChildrenIds(editingOrganization.value)]
    : [];

  return buildTree(organizations.value.filter(org => !excludeIds.includes(org.id)));
});

// 方法
const buildTree = (flatData: Organization[]): Organization[] => {
  const map = new Map();
  const roots: Organization[] = [];

  // 创建映射
  flatData.forEach(item => {
    map.set(item.id, { ...item, children: [] });
  });

  // 构建树结构
  flatData.forEach(item => {
    const node = map.get(item.id);
    if (item.parentId && map.has(item.parentId)) {
      map.get(item.parentId).children.push(node);
    } else {
      roots.push(node);
    }
  });

  return roots;
};

const getChildrenIds = (org: Organization): number[] => {
  const ids: number[] = [];
  if (org.children) {
    org.children.forEach(child => {
      ids.push(child.id);
      ids.push(...getChildrenIds(child));
    });
  }
  return ids;
};

const getTypeText = (type: string) => {
  const typeMap = {
    company: '公司',
    department: '部门',
    team: '团队',
    group: '小组'
  };
  return typeMap[type] || type;
};

const getTypeTagType = (type: string) => {
  const typeMap = {
    company: '',
    department: 'success',
    team: 'warning',
    group: 'info'
  };
  return typeMap[type] || '';
};

const getStatusText = (status: string) => {
  return status === 'active' ? '启用' : '禁用';
};

const getStatusTagType = (status: string) => {
  return status === 'active' ? 'success' : 'danger';
};

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
};

const handleEdit = (organization: Organization) => {
  editingOrganization.value = organization;
  Object.assign(organizationForm, {
    name: organization.name,
    code: organization.code,
    type: organization.type,
    parentId: organization.parentId,
    managerId: organization.managerId,
    description: organization.description || '',
    sort: organization.sort,
    status: organization.status,
  });
  showCreateDialog.value = true;
};

const handleAddChild = (parentOrganization: Organization) => {
  editingOrganization.value = null;
  resetForm();
  organizationForm.parentId = parentOrganization.id;
  showCreateDialog.value = true;
};

const handleDelete = async (organization: Organization) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除组织 "${organization.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    // 这里调用删除API
    console.log('删除组织:', organization.id);
    ElMessage.success('删除成功');
    await refreshOrganizations();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除组织失败:', error);
      ElMessage.error('删除失败');
    }
  }
};

const handleSubmit = async () => {
  try {
    await organizationFormRef.value.validate();
    submitting.value = true;

    if (editingOrganization.value) {
      // 更新组织
      console.log('更新组织:', editingOrganization.value.id, organizationForm);
      ElMessage.success('更新成功');
    } else {
      // 创建组织
      console.log('创建组织:', organizationForm);
      ElMessage.success('创建成功');
    }

    showCreateDialog.value = false;
    await refreshOrganizations();
  } catch (error) {
    console.error('提交失败:', error);
    ElMessage.error('操作失败');
  } finally {
    submitting.value = false;
  }
};

const resetForm = () => {
  editingOrganization.value = null;
  Object.assign(organizationForm, {
    name: '',
    code: '',
    type: 'department',
    parentId: undefined,
    managerId: undefined,
    description: '',
    sort: 0,
    status: 'active',
  });
  organizationFormRef.value?.clearValidate();
};

const refreshOrganizations = async () => {
  try {
    // 这里调用获取组织列表API
    console.log('刷新组织列表');
    await loadMockData();
  } catch (error) {
    console.error('获取组织列表失败:', error);
    ElMessage.error('获取数据失败');
  }
};

const loadUsers = async () => {
  try {
    // 这里调用获取用户列表API
    console.log('加载用户列表');
    // Mock数据
    users.value = [
      { id: 1, name: '张三', username: 'zhangsan', email: '<EMAIL>', role: 'admin', status: 'active', createdAt: '', updatedAt: '' },
      { id: 2, name: '李四', username: 'lisi', email: '<EMAIL>', role: 'manager', status: 'active', createdAt: '', updatedAt: '' },
      { id: 3, name: '王五', username: 'wangwu', email: '<EMAIL>', role: 'member', status: 'active', createdAt: '', updatedAt: '' },
    ];
  } catch (error) {
    console.error('获取用户列表失败:', error);
  }
};

const loadMockData = async () => {
  // Mock组织数据
  organizations.value = [
    {
      id: 1,
      name: '总公司',
      code: 'COMPANY',
      type: 'company',
      parentId: undefined,
      managerId: 1,
      description: '公司总部',
      level: 1,
      sort: 1,
      status: 'active',
      memberCount: 50,
      createdAt: '2024-01-01',
      updatedAt: '2024-01-01'
    },
    {
      id: 2,
      name: '技术部',
      code: 'TECH',
      type: 'department',
      parentId: 1,
      managerId: 2,
      description: '技术研发部门',
      level: 2,
      sort: 1,
      status: 'active',
      memberCount: 20,
      createdAt: '2024-01-01',
      updatedAt: '2024-01-01'
    },
    {
      id: 3,
      name: '前端团队',
      code: 'FRONTEND',
      type: 'team',
      parentId: 2,
      managerId: 3,
      description: '前端开发团队',
      level: 3,
      sort: 1,
      status: 'active',
      memberCount: 8,
      createdAt: '2024-01-01',
      updatedAt: '2024-01-01'
    },
    {
      id: 4,
      name: '后端团队',
      code: 'BACKEND',
      type: 'team',
      parentId: 2,
      managerId: 2,
      description: '后端开发团队',
      level: 3,
      sort: 2,
      status: 'active',
      memberCount: 12,
      createdAt: '2024-01-01',
      updatedAt: '2024-01-01'
    },
    {
      id: 5,
      name: '市场部',
      code: 'MARKET',
      type: 'department',
      parentId: 1,
      managerId: 1,
      description: '市场营销部门',
      level: 2,
      sort: 2,
      status: 'active',
      memberCount: 15,
      createdAt: '2024-01-01',
      updatedAt: '2024-01-01'
    }
  ];

  // 设置默认展开的节点
  expandedKeys.value = [1, 2];
};

// 生命周期
onMounted(async () => {
  await Promise.all([
    refreshOrganizations(),
    loadUsers()
  ]);
});
</script>

<style scoped>
.organization {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-section {
  margin-bottom: 20px;
}

.organization-tree-card {
  margin-bottom: 20px;
}

.organization-tree {
  margin-top: 10px;
}

.tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 5px 0;
}

.node-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.node-icon {
  color: #409eff;
}

.node-name {
  font-weight: 500;
  color: #303133;
}

.node-type,
.node-status {
  margin-left: 5px;
}

.node-member-count {
  color: #909399;
  font-size: 12px;
}

.node-actions {
  display: flex;
  gap: 5px;
  opacity: 0;
  transition: opacity 0.3s;
}

.tree-node:hover .node-actions {
  opacity: 1;
}
</style>
